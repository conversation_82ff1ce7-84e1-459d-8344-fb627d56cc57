2025-07-22 15:26:06.938 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-22 15:26:06.995 | Loaded RSA public key for plugin verification
2025-07-22 15:26:07.224 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-22 15:26:07.224 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-22 15:26:07.224 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-22 15:26:07.236 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-22 15:26:07.241 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-22 15:26:07.241 | Using Consul URL: consul:8500
2025-07-22 15:26:07.551 | Brain service listening at http://0.0.0.0:5070
2025-07-22 15:26:07.566 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-22 15:26:07.654 | Anthropic Service created, <PERSON><PERSON><PERSON><PERSON> starts sk-ant
2025-07-22 15:26:07.668 | Loaded service: AntService
2025-07-22 15:26:07.678 | GG Gemini Service created, ApiKey starts AIzaSy
2025-07-22 15:26:07.685 | Loaded service: GGService
2025-07-22 15:26:07.790 | Groq Service created, ApiKey starts gsk_m0
2025-07-22 15:26:07.791 | GroqService initialized with API key: Set (length: 56)
2025-07-22 15:26:07.802 | Loaded service: groq
2025-07-22 15:26:07.810 | Huggingface Service created with API key: Set (length: 37)
2025-07-22 15:26:07.811 | Loaded service: HFService
2025-07-22 15:26:07.817 | Mistral Service created, ApiKey starts AhDwC8
2025-07-22 15:26:07.818 | Loaded service: MistralService
2025-07-22 15:26:07.820 | OpenAI Service created, ApiKey starts sk-LaE
2025-07-22 15:26:07.835 | Loaded service: OAService
2025-07-22 15:26:07.835 | OpenRouter Service created, ApiKey starts sk-or-
2025-07-22 15:26:07.835 | Loaded service: ORService
2025-07-22 15:26:07.835 | Openweb Service created, ApiKey starts eyJhbG
2025-07-22 15:26:07.835 | Using default OpenWebUI URL: https://knllm.dusdusdusd.com
2025-07-22 15:26:07.835 | Loaded service: OWService
2025-07-22 15:26:07.835 | modelManager Loaded 8 services.
2025-07-22 15:26:07.835 | Loaded interface: anthropic
2025-07-22 15:26:07.847 | Loaded interface: gemini
2025-07-22 15:26:08.871 | Loaded interface: groq
2025-07-22 15:26:08.909 | Loaded interface: huggingface
2025-07-22 15:26:08.911 | Loaded interface: mistral
2025-07-22 15:26:08.916 | Loaded interface: openai
2025-07-22 15:26:08.931 | Loaded interface: openrouter
2025-07-22 15:26:08.937 | OpenWebUIInterface initialized with DEFAULT_TIMEOUT: 300000ms
2025-07-22 15:26:08.937 | Loaded interface: openwebui
2025-07-22 15:26:08.937 | modelManager Loaded 8 interfaces.
2025-07-22 15:26:08.975 | Loaded model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-22 15:26:08.975 | Loaded model: suno/bark
2025-07-22 15:26:08.975 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-22 15:26:08.975 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-22 15:26:08.975 | Loaded model: anthropic/claude-2
2025-07-22 15:26:08.975 | Loaded model: codellama/CodeLlama-34b-Instruct-hf
2025-07-22 15:26:08.976 | Loaded model: THUDM/cogvlm-chat-hf
2025-07-22 15:26:08.977 | Loaded model: openai/dall-e-2
2025-07-22 15:26:08.980 | Loaded model: openai/dall-e-3
2025-07-22 15:26:08.982 | Loaded model: deepseek-ai/DeepSeek-R1
2025-07-22 15:26:08.991 | Loaded model: or/cognitivecomputations/dolphin3.0-mistral-24b:free
2025-07-22 15:26:08.994 | Loaded model: openai/whisper-large-v3
2025-07-22 15:26:09.031 | Loaded model: openai/gpt-4.1-nano
2025-07-22 15:26:09.032 | Loaded model: openai/gpt-4-vision-preview
2025-07-22 15:26:09.039 | Loaded model: nousresearch/hermes-3-llama-3.1-405b
2025-07-22 15:26:09.039 | Loaded model: or/moonshotai/kimi-k2:free
2025-07-22 15:26:09.041 | KNLLMModel initialized with OpenWebUI interface
2025-07-22 15:26:09.043 | Loaded model: openweb/knownow
2025-07-22 15:26:09.045 | Loaded model: liquid/lfm-40b
2025-07-22 15:26:09.049 | Loaded model: meta-llama/llama-3.2-11b-vision-instruct
2025-07-22 15:26:09.054 | GroqService availability check: Available
2025-07-22 15:26:09.054 | GroqService ready state: Ready
2025-07-22 15:26:09.054 | GroqService is available and ready to use.
2025-07-22 15:26:09.054 | Loaded model: groq/llama-4
2025-07-22 15:26:09.056 | Loaded model: meta-llama/Llama-2-70b-chat-hf
2025-07-22 15:26:09.062 | Loaded model: liuhaotian/llava-v1.5-13b
2025-07-22 15:26:09.064 | Loaded model: microsoft/Phi-3.5-vision-instruct
2025-07-22 15:26:09.068 | MistralService availability check: Available
2025-07-22 15:26:09.068 | MistralService is available and ready to use.
2025-07-22 15:26:09.068 | Loaded model: mistral/mistral-small-latest
2025-07-22 15:26:09.074 | Loaded model: mistralai/Mistral-Nemo-Instruct-2407
2025-07-22 15:26:09.077 | Loaded model: facebook/musicgen-large
2025-07-22 15:26:09.078 | Loaded model: or/deepseek-ai/DeepSeek-R1:free
2025-07-22 15:26:09.092 | MistralService availability check: Available
2025-07-22 15:26:09.092 | MistralService is available and ready to use.
2025-07-22 15:26:09.092 | Loaded model: mistral/pixtral-12B-2409
2025-07-22 15:26:09.092 | Loaded model: facebook/seamless-m4t-large
2025-07-22 15:26:09.092 | Loaded model: stabilityai/stable-diffusion-xl-base-1.0
2025-07-22 15:26:09.092 | Loaded model: bigcode/starcoder
2025-07-22 15:26:09.096 | Loaded model: openai/tts
2025-07-22 15:26:09.098 | Loaded model: openai/whisper-large-v3
2025-07-22 15:26:09.100 | Loaded model: openai/whisper
2025-07-22 15:26:09.100 | modelManager Loaded 32 models.
2025-07-22 15:26:09.140 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-22 15:26:09.188 | [Brain] Attempting to restore model performance data from Librarian...
2025-07-22 15:26:09.198 | Service Brain registered with Consul
2025-07-22 15:26:09.200 | Successfully registered Brain with Consul
2025-07-22 15:26:09.227 | Brain registered successfully with PostOffice
2025-07-22 15:26:09.590 | Created ServiceTokenManager for Brain
2025-07-22 15:26:11.142 | [Brain] Successfully restored 38 model performance records from Librarian
2025-07-22 15:26:42.718 | Connected to RabbitMQ
2025-07-22 15:26:42.747 | Channel created successfully
2025-07-22 15:26:42.747 | RabbitMQ channel ready
2025-07-22 15:26:42.834 | Connection test successful - RabbitMQ connection is stable
2025-07-22 15:26:42.836 | Creating queue: brain-Brain
2025-07-22 15:26:42.857 | Binding queue to exchange: stage7
2025-07-22 15:26:42.903 | Successfully connected to RabbitMQ and set up queues/bindings