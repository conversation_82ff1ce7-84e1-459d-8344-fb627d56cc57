import React, { useState } from 'react';
import './UserInputModal.css';

export type AnswerType = 'text' | 'number' | 'boolean' | 'multipleChoice' | 'file';

interface UserInputModalProps {
    requestId: string;
    question: string;
    choices?: string[];
    answerType: AnswerType;
    onSubmit: (requestId: string, response: any) => void;
    onClose: () => void;
}

const UserInputModal: React.FC<UserInputModalProps> = ({ requestId, question, choices, answerType, onSubmit, onClose }) => {
    const [response, setResponse] = useState<string | number | boolean>('');
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const handleSubmit = async () => {
        try {
            if (answerType === 'file') {
                if (!selectedFile) {
                    alert('Please select a file');
                    return;
                }

                // Create FormData for file upload
                const formData = new FormData();
                formData.append('requestId', requestId);
                formData.append('files', selectedFile);

                // Submit file upload
                const response = await fetch('http://localhost:5020/submitUserInput', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Failed to upload file');
                }

                onClose();
            } else {
                await onSubmit(requestId, response);
                onClose();
            }
        } catch (error) {
            console.error('Error submitting user input:', error instanceof Error ? error.message : error);
        }
    };

    const renderInput = () => {
        switch (answerType) {
            case 'text':
                return <input type="text" value={response as string} onChange={(e) => setResponse(e.target.value)} />;
            case 'number':
                return <input type="number" value={response as number} onChange={(e) => setResponse(Number(e.target.value))} />;
            case 'boolean':
                return (
                    <div>
                        <button onClick={() => setResponse(true)}>Yes</button>
                        <button onClick={() => setResponse(false)}>No</button>
                    </div>
                );
            case 'multipleChoice':
                return (
                    <div>
                        {choices?.map((choice) => (
                            <button key={choice} onClick={() => setResponse(choice)}>{choice}</button>
                        ))}
                    </div>
                );
            case 'file':
                return (
                    <div>
                        <input
                            type="file"
                            onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                            accept=".txt,.md,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.csv,.json,.xml,.yaml,.yml,.png,.jpg,.jpeg,.gif,.svg,.bmp,.zip,.tar,.gz,.7z"
                        />
                        {selectedFile && <p>Selected: {selectedFile.name}</p>}
                    </div>
                );
        }
    };

    return (
        <div className="modal user-input-modal">
            <div className="modal-content">
                <h2>User Input Required</h2>
                <p className="modal-question">{question}</p>
                <div className="modal-input">{renderInput()}</div>
                <div className="modal-actions">
                    <button className="modal-submit" onClick={handleSubmit}>Submit</button>
                    <button className="modal-cancel" onClick={onClose}>Cancel</button>
                </div>
            </div>
            <div className="modal-backdrop" onClick={onClose}></div>
        </div>
    );
};

export default UserInputModal;